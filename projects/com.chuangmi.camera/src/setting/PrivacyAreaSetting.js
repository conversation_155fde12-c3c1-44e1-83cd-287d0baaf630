import React from 'react';
import {
  ScrollView,
  View,
  Text,
  Image,
  Dimensions,
  StyleSheet,
} from 'react-native';

import {imiThemeManager} from '../../../../imilab-design-ui';
import ListItem from '../../../../imilab-design-ui/src/widgets/settingUI/ListItem';
import ListItmeWithSwitch from '../../../../imilab-design-ui/src/widgets/settingUI/ListItmeWithSwitch';
import MessageDialog from '../../../../imilab-design-ui/src/widgets/settingUI/MessageDialog';
import I18n from '../../../../globalization/Localize';
import BaseDeviceComponent from '../../../../imilab-rn-sdk/components/BaseDeviceComponent';
import {LetDevice} from '../../../../imilab-rn-sdk/native/iot-kit/IMIDevice';
import {LetIMIIotRequest, IMIGotoPage} from '../../../../imilab-rn-sdk';
import {showToast, showLoading} from '../../../../imilab-design-ui';
import {stringsTo} from '../../../../globalization/Localize';
import NavigationBar from '../../../../imi-rn-commonView/NavigationBar/NavigationBar';

const ScreenOptions = Dimensions.get('window');
const viewWidth = ScreenOptions.width - 24 * 2;
const viewHeight = viewWidth * 9 / 16;
// 将直播流播放器分成8*4个矩形块
const itemWidth = viewWidth / 8;
const itemHeight = viewHeight / 4;
const REACT_MARGIN = 3; // 矩形线框到画面边缘的外边距

const tag = 'PrivacyAreaSetting';

// 隐私区域物模型ID常量
const PROPERTY_IDS = {
  VIRTUAL_FENCE_DETECTION: '100000',  // 虚拟围栏检测开关
  VIRTUAL_FENCE_AREA_DATA: '100001',  // 虚拟围栏区域数据
  PRIVACY_AREA_SWITCH: '100006',      // 区域隐私功能开关（主开关）
  PRIVACY_AREA_DATA: '100007',        // 隐私区域数据
};

export default class PrivacyAreaSetting extends BaseDeviceComponent {
  constructor(props) {
    super(props);
    this.state = {
      // 隐私区域相关状态
      privacyAreaSwitch: false, // 隐私区域保护总开关UI状态
      privacyAreaActualEnabled: false, // 真实设置状态，用于控制子组件显示
      privacyAreaData: null, // 隐私区域坐标数据

      // 虚拟围栏相关状态
      virtualFenceEnabled: false, // 虚拟围栏检测开关
      fenceAreaData: null, // 虚拟围栏区域数据

      // UI状态
      areaType: 0, // 区域样式类型 0-3
      showAIDialog: false,

      // 其他AI功能状态（用于提醒冲突）
      fenceSwitch: false,
      peopleSwitch: false,
      motionSwitch: false,
    };
    // 矩形线框的左上角和右下键的x、y坐标轴
    this.rectDatas = [REACT_MARGIN, REACT_MARGIN, viewWidth - REACT_MARGIN, viewHeight - REACT_MARGIN];
  }

  componentDidMount() {
    this._getAllData();
    this._subscribeFocus = this.props.navigation.addListener('focus', () => {
      // 从编辑页面返回时刷新数据
      this._getAllData();
    });
  }

  componentWillUnmount() {
    this._subscribeFocus && this._subscribeFocus();
    showLoading(false);
  }

  // 获取隐私区域保护相关数据 - 参考VirtualFenceSetting的实现
  async _getAllData() {
    try {
      showLoading(stringsTo('commWaitText'), true);
      console.log('开始获取隐私区域设置数据...');

      // 使用Promise.allSettled等待所有请求完成
      const promises = [
        LetDevice.getSingleProperty(PROPERTY_IDS.VIRTUAL_FENCE_DETECTION),
        LetDevice.getSingleProperty(PROPERTY_IDS.VIRTUAL_FENCE_AREA_DATA),
        LetDevice.getSingleProperty(PROPERTY_IDS.PRIVACY_AREA_SWITCH),
        LetDevice.getSingleProperty(PROPERTY_IDS.PRIVACY_AREA_DATA),
      ];

      const results = await Promise.allSettled(promises);

      let newStates = {
        privacyAreaSwitch: false,
        privacyAreaActualEnabled: false,
        virtualFenceEnabled: false,
        privacyAreaData: null,
        fenceAreaData: null,
        areaType: 0,
        motionSwitch: false,
        peopleSwitch: false,
        fenceSwitch: false
      };

      // 处理虚拟围栏检测开关 (100000)
      if (results[0].status === 'fulfilled') {
        const data = results[0].value;
        console.log('虚拟围栏检测开关--------' + JSON.stringify(data));
        if (data?.value?.code == 0) {
          const isEnabled = data.value.value || false;
          newStates.virtualFenceEnabled = isEnabled;
          console.log('虚拟围栏检测开关:', data.value.value);
        }
      } else {
        console.error('获取虚拟围栏检测开关失败:', results[0].reason);
      }

      // 处理虚拟围栏区域数据 (100001)
      if (results[1].status === 'fulfilled') {
        const data = results[1].value;
        console.log('虚拟围栏区域数据--------' + JSON.stringify(data));
        if (data?.value?.code == 0 && data.value.value) {
          newStates.fenceAreaData = data.value.value;
          console.log('虚拟围栏区域数据:', data.value.value);
        }
      } else {
        console.error('获取虚拟围栏区域数据失败:', results[1].reason);
      }

      // 处理隐私区域开关 (100006) - 主开关
      if (results[2].status === 'fulfilled') {
        const data = results[2].value;
        console.log('隐私区域开关--------' + JSON.stringify(data));
        if (data?.value?.code == 0) {
          const isEnabled = data.value.value || false;
          newStates.privacyAreaSwitch = isEnabled;
          newStates.privacyAreaActualEnabled = isEnabled; // 同时设置真实状态
          console.log('隐私区域开关:', data.value.value);
        }
      } else {
        console.error('获取隐私区域开关失败:', results[2].reason);
      }

      // 处理隐私区域数据 (100007)
      if (results[3].status === 'fulfilled') {
        const data = results[3].value;
        console.log('隐私区域数据--------' + JSON.stringify(data));
        if (data?.value?.code == 0 && data.value.value) {
          newStates.privacyAreaData = data.value.value;
          console.log('隐私区域数据:', data.value.value);
        }
      } else {
        console.error('获取隐私区域数据失败:', results[3].reason);
      }

      // 检查是否有请求失败
      const hasFailures = results.some(result => result.status === 'rejected');
      if (hasFailures) {
        console.error('部分隐私区域设置获取失败');
        showToast(stringsTo('commLoadingFailText'));
      }

      this.setState(newStates, () => {
        // 如果有隐私区域数据，解析坐标
        if (this.state.privacyAreaData) {
          this._parsePrivacyAreaData(this.state.privacyAreaData);
        }
      });

      console.log('隐私区域设置数据加载完成');

    } catch (error) {
      console.error('获取隐私区域设置失败:', error);
      showToast(stringsTo('commLoadingFailText'));
      this.setState({
        privacyAreaSwitch: false,
        privacyAreaActualEnabled: false,
        virtualFenceEnabled: false
      });
    } finally {
      showLoading(false);
    }
  }

  // 解析隐私区域数据 - 参考VirtualFenceSetting的实现
  _parsePrivacyAreaData(dataValue) {
    try {
      console.log('解析隐私区域数据:', dataValue);

      // 解析JSON字符串
      const parsedData = JSON.parse(dataValue);
      console.log('解析后的JSON数据:', JSON.stringify(parsedData, null, 2));

      // 提取隐私区域数据：privacy_area
      const areaData = parsedData?.privacy_area?.[0]?.sensor0?.[0]?.area;

      if (areaData && Array.isArray(areaData) && areaData.length === 4) {
        console.log('提取到的隐私区域坐标:', areaData);

        // 将百分比坐标转换为像素坐标
        const coordsArray = [
          areaData[0] / 100.0 * viewWidth,
          areaData[1] / 100.0 * viewHeight,
          areaData[2] / 100.0 * viewWidth,
          areaData[3] / 100.0 * viewHeight
        ];

        // 修正边界误差
        coordsArray[0] < REACT_MARGIN ? coordsArray[0] = REACT_MARGIN : null;
        coordsArray[1] < REACT_MARGIN ? coordsArray[1] = REACT_MARGIN : coordsArray[1] - 1.5;
        coordsArray[2] > viewWidth - REACT_MARGIN ? coordsArray[2] = viewWidth - REACT_MARGIN : coordsArray[2] = coordsArray[2] - 2;
        coordsArray[3] > viewHeight - REACT_MARGIN ? coordsArray[3] = viewHeight - REACT_MARGIN : coordsArray[3] = coordsArray[3] - 1.5;

        this.rectDatas = coordsArray;
        console.log('转换后的像素坐标:', this.rectDatas);
      } else {
        console.error('无法提取隐私区域坐标数据，areaData:', areaData);
        // 使用默认坐标
        this.rectDatas = [REACT_MARGIN, REACT_MARGIN, viewWidth - REACT_MARGIN, viewHeight - REACT_MARGIN];
      }
    } catch (error) {
      console.error('解析隐私区域数据失败:', error);
      // 使用默认坐标
      this.rectDatas = [REACT_MARGIN, REACT_MARGIN, viewWidth - REACT_MARGIN, viewHeight - REACT_MARGIN];
    }
  }

  // 解析区域坐标数据并转换为UI坐标（兼容旧格式）
  _parseAreaDataValue(coordsArrayString) {
    try {
      let coordsStringArray = coordsArrayString.replace("],", "]_").split('_');
      let coordsArray = [...JSON.parse(coordsStringArray[0]), ...JSON.parse(coordsStringArray[1])];
      coordsArray = [
        coordsArray[0] / 100.0 * viewWidth,
        coordsArray[1] / 100.0 * viewHeight,
        coordsArray[2] / 100.0 * viewWidth,
        coordsArray[3] / 100.0 * viewHeight
      ];

      // 修正边界误差
      coordsArray[0] < REACT_MARGIN ? coordsArray[0] = REACT_MARGIN : null;
      coordsArray[1] < REACT_MARGIN ? coordsArray[1] = REACT_MARGIN : coordsArray[1] - 1.5;
      coordsArray[2] > viewWidth - REACT_MARGIN ? coordsArray[2] = viewWidth - REACT_MARGIN : coordsArray[2] = coordsArray[2] - 2;
      coordsArray[3] > viewHeight - REACT_MARGIN ? coordsArray[3] = viewHeight - REACT_MARGIN : coordsArray[3] = coordsArray[3] - 1.5;

      this.rectDatas = coordsArray;
    } catch (e) {
      console.log('解析区域坐标失败:', e);
    }
  }

  // 隐私区域保护开关变更
  _onPrivacyAreaSwitchChange(value) {
    // 检查是否与其他AI功能冲突
    if (value && (this.state.fenceSwitch || this.state.peopleSwitch || this.state.motionSwitch)) {
      this.setState({ showAIDialog: true });
      return;
    }

    this._setPrivacyAreaProperty(value);
  }

  // 设置隐私区域开关的通用函数
  async _setPrivacyAreaProperty(value) {
    // 立即更新UI状态，给用户即时反馈
    this.setState({ privacyAreaSwitch: value });

    try {
      showLoading(stringsTo('commWaitText'), true);
      console.log('设置隐私区域开关:', value);

      const paramJson = JSON.stringify({ msg_id: PROPERTY_IDS.PRIVACY_AREA_SWITCH, value: value });

      await LetDevice.setProperties(true, LetDevice.deviceID, PROPERTY_IDS.PRIVACY_AREA_SWITCH, paramJson);

      // 设置成功后更新真实状态，控制子组件显示
      this.setState({ privacyAreaActualEnabled: value });
      showToast(stringsTo('settings_set_success'));
      console.log('隐私区域开关设置成功:', value);

    } catch (error) {
      console.error('设置隐私区域开关失败:', error);
      showToast(stringsTo('operationFailed'));
      // 设置失败时恢复到原来的状态
      this.setState({ privacyAreaSwitch: !value });
    } finally {
      showLoading(false);
    }
  }

  // 从区域编辑页面返回的回调
  _refreshEffectiveMonitorArea(rectangleCoords, areaType) {
    this.rectDatas = rectangleCoords;
    this.setState({ areaType });
  }

  render() {
    return (
      <View style={styles.container}>
        <NavigationBar
          title={stringsTo('privacy_area_protection')}
          left={[
            {
              key: NavigationBar.ICON.BACK,
              onPress: () => {
                this.props.navigation.canGoBack() ? this.props.navigation.goBack() : IMIGotoPage.exit();
              },
              accessibilityLabel: 'privacy_area_back',
            },
          ]}
          right={[]}
        />

        <ScrollView showsVerticalScrollIndicator={false}>
          <ListItmeWithSwitch
            title={stringsTo('privacy_area_protection')}
            value={this.state.privacyAreaSwitch}
            onValueChange={(value) => this._onPrivacyAreaSwitchChange(value)}
            accessibilityLabel={['privacy_area_on', 'privacy_area_off']}
          />

          {this.state.privacyAreaActualEnabled ? (
            <ListItem
              title={stringsTo('area_privacy_edit')}
              onPress={() => {
                // 模拟围栏区域数据（应该比隐私区域大）
                const fenceAreaData = [30, 20, 250, 150];

                this.props.navigation.push('PrivacyAreaModifyPage', {
                  areaData: [...this.rectDatas], // 隐私区域坐标
                  areaType: this.state.areaType,
                  fenceSwitch: this.state.fenceSwitch,
                  fenceData: fenceAreaData, // 围栏区域坐标
                  callback: (areaDataNew, type) => this._refreshEffectiveMonitorArea([...areaDataNew], type)
                });
              }}
              accessibilityLabel={'privacy_area_edit'}
            />
          ) : null}
          
          <View style={{ height: 40 }} />
        </ScrollView>

        {this._renderAIDialog()}
      </View>
    );
  }


  // AI功能冲突提醒对话框
  _renderAIDialog() {
    return (
      <MessageDialog
        visible={this.state.showAIDialog}
        title={stringsTo('tips')}
        canDismiss={true}
        onDismiss={() => {
          this.setState({ showAIDialog: false });
        }}
        buttons={[
          {
            text: I18n.t('ok_button'),
            callback: () => {
              this.setState({ showAIDialog: false });
            },
          },
        ]}>
        <Text style={styles.dialogText}>
          {stringsTo('private_open_msg')}
        </Text>
      </MessageDialog>
    );
  }
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: imiThemeManager.theme.pageBg,
  },
  dialogText: {
    fontSize: 14,
    color: imiThemeManager.theme.textColor,
    lineHeight: 20,
    textAlign: 'center',
  },
});