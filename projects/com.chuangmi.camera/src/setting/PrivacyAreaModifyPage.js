import React from 'react';
import {
  View,
  Text,
  Image,
  ImageBackground,
  Dimensions,
  PanResponder,
  Platform,
  BackHandler,
  PixelRatio,
  StyleSheet,
  TouchableOpacity,
} from 'react-native';

import { Surface, Shape, Path } from '@react-native-community/art';

import NavigationBar from '../../../../imi-rn-commonView/NavigationBar/NavigationBar';
import {IMIGotoPage} from '../../../../imilab-rn-sdk';
import {imiThemeManager, MessageDialog, showToast, showLoading} from '../../../../imilab-design-ui';
import ListItmeWithSwitch from '../../../../imilab-design-ui/src/widgets/settingUI/ListItmeWithSwitch';
import ListItem from '../../../../imilab-design-ui/src/widgets/settingUI/ListItem';
import {stringsTo} from '../../../../globalization/Localize';
import {LetDevice} from '../../../../imilab-rn-sdk/native/iot-kit/IMIDevice';

const ScreenOptions = Dimensions.get('window');
const viewWidth = ScreenOptions.width - 24 * 2;
const viewHeight = (viewWidth * 9) / 16;
// 将直播流播放器分成8*4个矩形块
const itemWidth = viewWidth / 8;
const itemHeight = viewHeight / 4;
const REACT_MARGIN = 0; // 矩形线框的到画面边缘的外边距
const CIRCLE_RADIUS = 3; // 矩形线框这个角上实心圆的半径
const DEVIATION_VALUE = 20; // 线框宽度、圆半径、除法取整引起的误差，试用
const VALID_TOUCH_RANGE = 15; // 四个边角的有效触摸范围
const {width: screenWidth, height: screenHeight} = Dimensions.get('screen');
const options = [
  {
    name: '可爱1',
    source: require("../../resources/images/area_style_lovely2.webp"),
    backgroundColor: 'red',
    type: 3,
    bgSource: require('../../resources/images/area_style_bg_four.webp'),
  },
  {
    name: '可爱2',
    source: require("../../resources/images/area_style_lovely1.webp"),
    type: 2,
     backgroundColor: 'blue',
    bgSource: require('../../resources/images/area_style_bg_three.webp'),
  },
  {
    name: '可爱3',
    source: require("../../resources/images/area_style_pure1.webp"),
    type: 0,
    backgroundColor: 'yellow',
    bgSource: require('../../resources/images/area_style_bg_one.webp'),
  },
  {
    name: '可爱4',
    source: require("../../resources/images/area_style_pure2.webp"),
    type: 1,
     backgroundColor: 'green',
    bgSource: require('../../resources/images/area_style_bg_two.webp'),
  },
];
const TAG = 'PrivacyAreaModifyPage';
export default class PrivacyAreaModifyPage extends React.Component {
  static navigationOptions = navigation => {
    return {headerTransparent: true, header: null};
  };

  constructor(props, context) {
    super(props, context);
    // 默认使用可爱3样式 (type: 0)
    let type = this.props.route?.params?.areaType || 0;
    let index = options.findIndex(item => item.type == type);
    // 如果找不到对应的样式，默认使用可爱3（索引2）
    if (index === -1) {
      index = 2; // 可爱3的索引是2
      type = 0;  // 可爱3的type是0
    }
    this.state = {
      progressing: false,
      showCover: true,
      showBg: true,
      styleType: type,
      styleTempType: type,
      showStyleDialog: false,
      canSave: false,
      showSaveDialog: false,
      selectName: options[index].name,
      bgSource: options[index].bgSource||options[index].backgroundColor,
      hidePrivateAreaSwitch: false,
    };
    this.isMoving = false;
    this.curTempItem = options[index];
    this.timeStamp = Date.now();
    // 使用上一级页面传入的真实数据，去掉模拟数据
    this.rectDatas = this.props.route?.params?.areaData || [REACT_MARGIN, REACT_MARGIN, viewWidth - REACT_MARGIN, viewHeight - REACT_MARGIN];
    this.fenceDatas = this.props.route?.params?.fenceData || [REACT_MARGIN, REACT_MARGIN, viewWidth - REACT_MARGIN, viewHeight - REACT_MARGIN];
    this.fenceSwitch = this.props.route?.params?.fenceSwitch || false;
    // 有效看护区域矩形背景框的左上和右下角坐标
    this.rectBackGround = this.rectBackGround = [
      Math.floor(this.rectDatas[0] / itemWidth) * itemWidth,
      Math.floor(this.rectDatas[1] / itemHeight) * itemHeight,
      Math.ceil(this.rectDatas[2] / itemWidth) * itemWidth,
      Math.ceil(this.rectDatas[3] / itemHeight) * itemHeight,
    ];
    this.distanceData = [0, 0, 0, 0];
    this.touchPosition = 0; // 开始拖拽的点的坐标位或得出的值
    this.existsSettingsImg = false;
    this.panResponder = PanResponder.create({
      onStartShouldSetPanResponder: () => true, // 刚开始的时候
      onMoveShouldSetPanResponder: () => true,
      onShouldBlockNativeResponder: () => true,
      onPanResponderTerminationRequest: () => true, // 不允许其他人抢占。
      onPanResponderGrant: evt => {
        this.setState({showCover: false, showBg: false});
        let x = evt.nativeEvent.locationX; // 开始触摸点相对于父View的横坐标
        let y = evt.nativeEvent.locationY; // 开始触摸点相对于父View的纵坐标
        this.touchBeginCoordX = x;
        this.touchBeginCoordY = y;

        this.distanceData = [0, 0, 0, 0];
        let smallest = [VALID_TOUCH_RANGE, VALID_TOUCH_RANGE]; // 矩形框四个角的有效触摸范围，x轴和y轴方向均在VALID_TOUCH_RANGE以内
        let positionCoord = [0, 0]; // 用户开始触摸点的x和y轴坐标

        // 触摸点在线框左上角，则设定触摸坐标为[8,4]
        this.distanceData[0] = Math.abs(x - this.rectDatas[0]);
        if (this.distanceData[0] < smallest[0]) {
          // 触摸点在线框左上角坐标的x轴方向的有效范围内
          positionCoord[0] = 8;
        }
        this.distanceData[1] = Math.abs(y - this.rectDatas[1]);
        if (this.distanceData[1] < smallest[1]) {
          // 触摸点在线框左上角坐标y轴方向的有效范围内
          positionCoord[1] = 4;
        }

        // 触摸点在线框右下角，则设定触摸坐标为[2,1]
        this.distanceData[2] = Math.abs(x - this.rectDatas[2]);
        if (this.distanceData[2] < smallest[0]) {
          // 触摸点在线框右下角坐标的x轴方向的有效范围内
          positionCoord[0] = 2;
        }
        this.distanceData[3] = Math.abs(y - this.rectDatas[3]);
        if (this.distanceData[3] < smallest[1]) {
          // 触摸点在线框右下角坐标y轴方向的有效范围内
          positionCoord[1] = 1;
        }
        this.touchPosition = positionCoord[0] | positionCoord[1]; // 通过位或运算得出共有12，3，6，9四个值
      },

      onPanResponderMove: (evt, gestureState) => {
        /* let x = evt.nativeEvent.locationX;
        let y = evt.nativeEvent.locationY; */
        if (Math.abs(gestureState.dx) <= 5 && Math.abs(gestureState.dy) <= 5) {
          console.log('---------------------------没滑动距离');
          return;
        }
        // 通过触摸开始坐标加上横纵方向的位移算出当前坐标位置，可以解决拖动时locationX和locationY跳变问题
        let x = this.touchBeginCoordX + gestureState.dx; // dx 从触摸操作开始时的累计横向位移
        let y = this.touchBeginCoordY + gestureState.dy; // dy 从触摸操作开始时的累计纵向位移
        console.log(
          '===========is moving',
          this.touchBeginCoordX,
          gestureState.dx,
          this.touchBeginCoordY,
          gestureState.dy,
        );
        let pointChange = false;
        switch (this.touchPosition) {
          case 12: {
            // 拖动左上角 触摸点[8,4]

            if (x < this.rectDatas[2] - itemWidth + DEVIATION_VALUE) {
              pointChange = true;
              this.rectDatas[0] = x < 0 ? 0 : x;
              this.rectBackGround[0] = Math.floor(x / itemWidth) * itemWidth;
            }
            if (y < this.rectDatas[3] - itemHeight + DEVIATION_VALUE) {
              pointChange = true;
              this.rectDatas[1] = y < 0 ? 0 : y;
              this.rectBackGround[1] = Math.floor(y / itemHeight) * itemHeight;
            }
            break;
          }
          case 6: {
            // 拖动右上角 触摸点[2,4]
            if (x < viewWidth - REACT_MARGIN && x > this.rectDatas[0] + itemWidth - DEVIATION_VALUE) {
              pointChange = true;
              this.rectDatas[2] = x;
              this.rectBackGround[2] = Math.ceil(x / itemWidth) * itemWidth;
            }
            if (y >= REACT_MARGIN && y < this.rectDatas[3] - itemHeight + DEVIATION_VALUE) {
              pointChange = true;
              this.rectDatas[1] = y;
              this.rectBackGround[1] = Math.floor(y / itemHeight) * itemHeight;
            }
            break;
          }
          case 3: {
            // 拖动右下角 触摸点[2,1]
            if (x < viewWidth - REACT_MARGIN && x > this.rectDatas[0] + itemWidth - DEVIATION_VALUE) {
              pointChange = true;
              this.rectDatas[2] = x;
              this.rectBackGround[2] = Math.ceil(x / itemWidth) * itemWidth;
            }
            if (y < viewHeight - REACT_MARGIN && y > this.rectDatas[1] + itemHeight - DEVIATION_VALUE) {
              pointChange = true;
              this.rectDatas[3] = y;
              this.rectBackGround[3] = Math.ceil(y / itemHeight) * itemHeight;
            }
            break;
          }
          case 9: {
            // 拖动左下角 触摸点[8,1]
            if (x < this.rectDatas[2] - itemWidth + DEVIATION_VALUE) {
              pointChange = true;
              this.rectDatas[0] = x < 0 ? 0 : x;
              this.rectBackGround[0] = Math.floor(x / itemWidth) * itemWidth;
            }
            if (y < viewHeight - REACT_MARGIN && y > this.rectDatas[1] + itemHeight + DEVIATION_VALUE) {
              pointChange = true;
              this.rectDatas[3] = y;
              this.rectBackGround[3] = Math.ceil(y / itemHeight) * itemHeight;
            }
            break;
          }
        }
        if (pointChange) {
          this.setState({canSave: true});
        }
      },

      onPanResponderRelease: () => {
        console.log('onPanResponderRelease');
        this.cropImage();
      },

      onPanResponderTerminate: () => {
        console.log('onPanResponderTerminate');
      },
    });
  }

  componentDidMount() {
    if (Platform.OS === 'android') {
      BackHandler.addEventListener('hardwareBackPress', this.onBackHandler);
    }

    // 调试信息：打印坐标和绘制区域信息
    console.log('隐私区域编辑页面 - 绘制区域尺寸:', { viewWidth, viewHeight });
    console.log('隐私区域编辑页面 - 隐私区域坐标:', this.rectDatas);
    console.log('隐私区域编辑页面 - 围栏区域坐标:', this.fenceDatas);
    console.log('隐私区域编辑页面 - 围栏开关:', this.fenceSwitch);

    setTimeout(() => {}, 100);
  }

  async cropImage() {
    let imageSource = '';
    if (!this.existsSettingsImg) {
      imageSource = null;
    } else {
      if (Platform.OS !== 'ios') {
        imageSource = `?timestamp=${this.timeStamp}`;
      }
    }
    if (!this.existsSettingsImg) {
      // 未获取到截图，就不要去截图了
      this.setState({showBg: true, showCover: false});
      return;
    }
    try {
      let source = `file://${imageSource}`;
      if (Platform.OS === 'ios') {
        source = imageSource;
      }
      console.log('image source', source);

      Image.getSize(
        source,
        (width, height) => {
          console.log('pic info', width, height);
          // 分辨率差2倍
          // if (Platform.OS !== "ios" && height > 480) {
          //   width = 2 * width;
          //   height = 2 * height;
          // }
          // 864  480
          let targetName = `crop/crop_area_${Date.now()}.jpg`;
          // ios中同名targetName,无法正常显示图片，Android可正常显示
          // let targetName = `crop_area_${Date.now()}.jpg`
          let cropWidth = Math.abs(this.rectDatas[2] - this.rectDatas[0]);
          let cropHeight = Math.abs(this.rectDatas[3] - this.rectDatas[1]);

          let pxWidth = parseInt((width * cropWidth) / viewWidth);
          let pxHeight = parseInt((width * cropHeight) / viewWidth);
          let leftX = parseInt((width * this.rectDatas[0]) / viewWidth);
          let leftY = parseInt((height * this.rectDatas[1]) / viewHeight);

          let params = {
            offset: {x: leftX, y: leftY},
            size: {width: pxWidth, height: pxHeight},
            displaySize: {width: pxWidth, height: pxHeight},
          };

          console.log('crop params', params, viewWidth, viewHeight, pxWidth, pxHeight, leftX, leftY);
        },
        failure => {
          this.setState({showBg: true});
          console.log('failure', failure);
        },
      );
    } catch (e) {
      this.setState({showBg: true});
    }
  }

  renderTitleBar() {
    let titleBarContent = {
      title: stringsTo('area_privacy_edit'),
      type: this.state.darkMode ? NavigationBar.TYPE.black : NavigationBar.TYPE.white,
      left: [
        {
          key: this.state.canSave ? NavigationBar.ICON.CUSTOM : NavigationBar.ICON.BACK,
          n_source: this.state.canSave ? require('../../resources/images/close.png') : undefined,
          p_source: this.state.canSave ? require('../../resources/images/close.png') : undefined,
          onPress: () => {
            if (this.state.canSave) {
              this.setState({showSaveDialog: true});
              return;
            }
            this.props.navigation.goBack();
          },
        },
      ],
      right: this.state.canSave
        ? [
            {
              key: NavigationBar.ICON.COMPLETE,
              onPress: () => {
                this.onSubmit();
              },
            },
          ]
        : [],
      titleStyle: {
        fontSize: 18,
        color: '#333333',
        fontWeight: 500,
      },
    };
    return <NavigationBar {...titleBarContent} />;
  }

  // 点击保存看护区域坐标数据 - 参考VirtualFenceAreaSetting.js的实现
  onSubmit() {
    console.log('保存隐私区域坐标:', this.rectDatas);

    try {
      // 显示加载状态
      showLoading(stringsTo('commWaitText'), true);

      // 参考VirtualFenceAreaSetting.js：将像素坐标转换为百分比坐标
      let positions = [
        Math.ceil(this.rectDatas[0] / viewWidth * 100),
        Math.ceil(this.rectDatas[1] / viewHeight * 100),
        Math.ceil(this.rectDatas[2] / viewWidth * 100),
        Math.ceil(this.rectDatas[3] / viewHeight * 100)
      ];

      // 构建隐私区域数据格式
      const privacyAreaData = {
        "privacy_area": [{
          "sensor0": [{
            "area": [{
              "pos": [positions[0], positions[1], positions[2], positions[3]]
            }],
            "color": 0
          }]
        }]
      };

      const valueString = JSON.stringify(privacyAreaData);
      console.log("隐私区域数据:", valueString);

      // 按照物模型格式构建数据
      const paramJson = JSON.stringify({
        msg_id: '100007',
        value: valueString
      });
      console.log("格式化后的物模型数据:", paramJson);

      // 使用物模型方式设置数据
      LetDevice.setProperties(true, LetDevice.deviceID, '100007', paramJson).then((res) => {
        console.log("隐私区域数据设置成功:", JSON.stringify(res));

        // 设置成功后调用回调函数，将编辑后的坐标数据传回PrivacyAreaSetting页面
        const callback = this.props.route?.params?.callback;
        if (callback) {
          callback(this.rectDatas, this.state.styleType, this.state.hidePrivateAreaSwitch);
        }

        showToast(stringsTo('settings_set_success'));
        this.props.navigation.goBack();

      }).catch((err) => {
        console.error("设置隐私区域数据失败:", JSON.stringify(err));
        showToast(stringsTo('operationFailed'));
      }).finally(() => {
        showLoading(false);
      });

    } catch (error) {
      console.error("保存隐私区域数据异常:", error);
      showToast(stringsTo('operationFailed'));
      showLoading(false);
    }
  }

  render() {
    let imageSource = '';

    // 可拖拽线框的绘制路径
    let draggable_rectangle_path = Path()
      .moveTo(this.rectDatas[0], this.rectDatas[1])
      .lineTo(this.rectDatas[2], this.rectDatas[1])
      .lineTo(this.rectDatas[2], this.rectDatas[3])
      .lineTo(this.rectDatas[0], this.rectDatas[3])
      .close();

    // 表示有效看护区域的半透明矩形背景的绘制路径
    let background_path = Path()
      .moveTo(this.rectBackGround[0], this.rectBackGround[1])
      .lineTo(this.rectBackGround[2], this.rectBackGround[1])
      .lineTo(this.rectBackGround[2], this.rectBackGround[3])
      .lineTo(this.rectBackGround[0], this.rectBackGround[3])
      .close();

    let fence_path = Path()
      .moveTo(this.fenceDatas[0], this.fenceDatas[1])
      .lineTo(this.fenceDatas[2], this.fenceDatas[1])
      .lineTo(this.fenceDatas[2], this.fenceDatas[3])
      .lineTo(this.fenceDatas[0], this.fenceDatas[3])
      .close();

    let background_path_all = Path()
      .moveTo(0, 0)
      .lineTo(this.rectDatas[0], 0)
      .lineTo(this.rectDatas[0], viewHeight)
      .lineTo(0, viewHeight)
      .close();

    let background_path_one = Path()
      .moveTo(0, 0)
      .lineTo(this.rectDatas[0], 0)
      .lineTo(this.rectDatas[0], viewHeight)
      .lineTo(0, viewHeight)
      .close();

    let background_path_two = Path()
      .moveTo(this.rectDatas[0], 0)
      .lineTo(viewWidth, 0)
      .lineTo(viewWidth, this.rectDatas[1])
      .lineTo(this.rectDatas[0], this.rectDatas[1])
      .close();

    let background_path_three = Path()
      .moveTo(this.rectDatas[0], this.rectDatas[3])
      .lineTo(viewWidth, this.rectDatas[3])
      .lineTo(viewWidth, viewHeight)
      .lineTo(this.rectDatas[0], viewHeight)
      .close();

    let background_path_four = Path()
      .moveTo(this.rectDatas[2], this.rectDatas[1])
      .lineTo(viewWidth, this.rectDatas[1])
      .lineTo(viewWidth, this.rectDatas[3])
      .lineTo(this.rectDatas[2], this.rectDatas[3])
      .close();

    let top_left_circle = new Path()
      .moveTo(this.rectDatas[0] + CIRCLE_RADIUS, this.rectDatas[1])
      .arc(0, CIRCLE_RADIUS * 2, CIRCLE_RADIUS)
      .arc(0, -CIRCLE_RADIUS * 2, CIRCLE_RADIUS)
      .close();

    let top_right_circle = new Path()
      .moveTo(this.rectDatas[2] + CIRCLE_RADIUS, this.rectDatas[1])
      .arc(0, CIRCLE_RADIUS * 2, CIRCLE_RADIUS)
      .arc(0, -CIRCLE_RADIUS * 2, CIRCLE_RADIUS)
      .close();

    let bottom_right_circle = new Path()
      .moveTo(this.rectDatas[2] + CIRCLE_RADIUS, this.rectDatas[3] + 2 * CIRCLE_RADIUS)
      .arc(0, -CIRCLE_RADIUS * 2, CIRCLE_RADIUS)
      .arc(0, CIRCLE_RADIUS * 2, CIRCLE_RADIUS)
      .close();
    let bottom_left_circle = new Path()
      .moveTo(this.rectDatas[0] + CIRCLE_RADIUS, this.rectDatas[3] + 2 * CIRCLE_RADIUS)
      .arc(0, -CIRCLE_RADIUS * 2, CIRCLE_RADIUS)
      .arc(0, CIRCLE_RADIUS * 2, CIRCLE_RADIUS)
      .close();
    let cropWidth = Math.abs(this.rectDatas[2] - this.rectDatas[0]);
    let cropHeight = Math.abs(this.rectDatas[3] - this.rectDatas[1]);

    // TODO 需要替换ImageBackground图片
    return (
      <View
        style={{
          display: 'flex',
          height: '100%',
          width: '100%',
          flex: 1,
          flexDirection: 'column',
          backgroundColor: '#FFFFFF',
        }}>
        {this.renderTitleBar()}
        <Text
          style={{
            fontSize: 12,
            color: '#666666',
            marginLeft: 27,
            marginTop: 28,
          }}>
          {'隐私区域绘制'}
        </Text>
        <View {...this.panResponder.panHandlers}>
          <ImageBackground
            style={{
              width: viewWidth,
              height: viewHeight,
              marginHorizontal: 24,
              marginTop: 13,
              marginBottom: 20,
            }}
            imageStyle={{borderRadius: 0}}>
            {/* 展示截图或者无截图时的预制图片 */}
            {!this.state.showBg ? (
              <Image
                style={{
                  width: viewWidth,
                  height: viewHeight,
                }}
                source={imageSource}
                key={this.timeStamp}
              />
            ) : null}
            {/* 展示背景图 */}
            {this.state.showBg ? (
              <Image
                style={{
                  width: viewWidth,
                  height: viewHeight,
                }}
                resizeMode={'cover'}
                source={this.state.bgSource}
                key={this.timeStamp}
              />
            ) : null}

            {this.state.showCover ? (
              <View
                style={{
                  position: 'absolute',
                  width: cropWidth,
                  height: cropHeight,
                  left: this.rectDatas[0],
                  top: this.rectDatas[1],
                }}>
                <Image
                  source={this.state.coverImageUri}
                  style={{
                    width: cropWidth,
                    height: cropHeight,
                    resizeMode: 'contain',
                  }}
                  onError={error => {
                    console.log('image load error', error);
                  }}
                />
              </View>
            ) : null}

            <View style={{position: 'absolute'}}>
              <Surface width={viewWidth} height={viewHeight}>
                {/* <Shape d={background_path} fill="#32BAC0" opacity="0.3" /> */}
                {!this.state.showBg ? <Shape d={background_path_one} fill="#000000" opacity="0.5" /> : null}
                {!this.state.showBg ? <Shape d={background_path_two} fill="#000000" opacity="0.5" /> : null}
                {!this.state.showBg ? <Shape d={background_path_three} fill="#000000" opacity="0.5" /> : null}
                {!this.state.showBg ? <Shape d={background_path_four} fill="#000000" opacity="0.5" /> : null}
                <Shape d={draggable_rectangle_path} fill="#32BAC0" opacity="0" />
                <Shape d={draggable_rectangle_path} stroke="#32BAC0" strokeWidth={1} />
                {this.fenceSwitch ? (
                  <Shape d={fence_path} stroke="#32BAC0" strokeDash={[15, 10]} strokeWidth={1} />
                ) : null}
              </Surface>
            </View>
          </ImageBackground>
          <View
            style={{
              width: viewWidth + 6,
              height: viewHeight + 6,
              position: 'absolute',
              marginHorizontal: 21,
              marginTop: 10,
              marginBottom: 20,
            }}
            imageStyle={{borderRadius: 0}}>
            <Surface width={viewWidth + 6} height={viewHeight + 6}>
              <Shape d={top_left_circle} fill="#32BAC0" />
              <Shape d={top_right_circle} fill="#32BAC0" />
              <Shape d={bottom_right_circle} fill="#32BAC0" />
              <Shape d={bottom_left_circle} fill="#32BAC0" />
            </Surface>
          </View>
        </View>
        {/*<ListItemWithSwitch*/}
        {/*  title={LocalizedStrings['area_privacy_hide']}*/}
        {/*  value={this.state.hidePrivateAreaSwitch}*/}
        {/*  showSeparator={false}*/}
        {/*  titleStyle={{ fontWeight: 'bold' }}*/}
        {/*  onValueChange={(value) => this._onHideAreaChange(value)}*/}
        {/*/>*/}

        <ListItem
          title={'隐私区域样式'}
          showSeparator={false}
          titleStyle={{fontWeight: 'bold'}}
          value={this.state.selectName}
          onPress={() => {
            let index = options.findIndex(item => item.type == this.state.styleType);
            this.curTempItem = options[index];
            this.setState({showStyleDialog: true, styleTempType: this.state.styleType});
          }}
        />

        {/* <LoadingDialog
          visible={this.state.progressing}
          message={LocalizedStrings.c_setting}
          onModalHide={() => this.setState({progressing: false})}
        /> */}
        {this._renderStyleDialog()}
        {this._renderBackDialog()}
      </View>
    );
  }

  _renderStyleDialog() {
    return (
      <MessageDialog
        style={[styles.areaStyle]}
        visible={this.state.showStyleDialog}
        title={'baocun'}
        showSubtitle={false}
        canDismiss={false}
        onDismiss={() => {
          // this._repeatDismiss();
        }}
        // showTitle={false}
        // showButton={false}
        useNewTheme={true}
        showSeparator={false}
        buttons={[
          {
            text: stringsTo('cancel'),
            callback: () => {
              this.setState({showStyleDialog: false});
            },
          },
          {
            text: stringsTo('imi_save'),
            callback: () => {
              this.setState({
                canSave: true,
                showStyleDialog: false,
                styleType: this.curTempItem.type,
                bgSource: this.curTempItem.bgSource,
                selectName: this.curTempItem.name,
              });
            },
          },
        ]}>
        {this._renderStyleInnerView()}
      </MessageDialog>
    );
  }

  _renderStyleInnerView() {
    let useWidth = screenWidth;
    let width = (useWidth - 54) / 2;
    return (
      <View style={{flexDirection: 'row', flexWrap: 'wrap', marginHorizontal: 22, marginBottom: 0}}>
        {options.map((item, index) => {
          let textStyle = {
            paddingVertical: 5,
            color: this.state.styleTempType === item.type ? '#32BAC0' : '#CBCBCB',
          };
          return (
            <TouchableOpacity
              key={index}
              activeOpacity={1}
              onPress={() => {
                this.chooseBg(item, index);
              }}>
              <View key={index} style={{flexDirection: 'column', alignItems: 'center', justifyContent: 'center'}}>
                <View
                  style={[
                    {width: width, height: width, alignItems: 'center', justifyContent: 'center'},
                    this.state.styleTempType === item.type
                      ? {borderRadius: 16, borderColor: '#32BAC0', borderWidth: 2}
                      : {borderRadius: 16, borderColor: '#FFFFFF', borderWidth: 2},
                    index % 2 == 0 ? {marginRight: 10} : null,
                  ]}>
                  <Image source={item.source} style={{width: width - 12, height: width - 12, borderRadius: 16}} />
                </View>
                <Text style={textStyle}>{item.name}</Text>
              </View>
            </TouchableOpacity>
          );
        })}
      </View>
    );
  }

  _renderBackDialog() {
    return (
      <MessageDialog
        visible={this.state.showSaveDialog}
        message={'退出后，当前修改将不会保存，确定要退出吗？'}
        messageStyle={{textAlign: 'center'}}
        canDismiss={false}
        buttons={[
          {
            text: '取消',
            callback: _ => {
              this.setState({showSaveDialog: false});
            },
          },
          {
            text: '退出',
            callback: _ => {
              this.setState({showSaveDialog: false});
              this.props.navigation.goBack();
            },
          },
        ]}
      />
    );
  }
  /**
   * 选择隐私区域样式
   */
  chooseBg(item, index) {
    this.curTempItem = item;
    this.setState({styleTempType: item.type});
  }
  /**
   * 隐私区域隐藏开关
   * 更新2023年11月15日
   * 合并spec，这里不做值更改值保存临时值
   * @param value
   */
  _onHideAreaChange(value) {
    console.log('隐私区域开关', value);
    this.setState({hidePrivateAreaSwitch: value, canSave: true});
  }

  componentWillUnmount() {
    if (Platform.OS === 'android') {
      BackHandler.removeEventListener('hardwareBackPress', this.onBackHandler);
    }
  }

  onBackHandler = () => {
    // if (this.state.canSave) {
    //   this.onSubmit();
    //   return true;
    // }
    if (this.state.canSave) {
      this.setState({showSaveDialog: true});
      return true;
    }
    return false;
  };
}

const styles = StyleSheet.create({
  areaStyle: {
    width: screenWidth,
    bottom: 0,
    borderTopLeftRadius: 20,
    borderTopRightRadius: 20,
    borderBottomLeftRadius: 0,
    borderBottomRightRadius: 0,
    marginHorizontal: 0,
    backgroundColor: imiThemeManager.getTheme().pageBg,
  },
});